<x-app-layout title="Home - Vibrant Road Safety Awareness Foundation" description="Promoting road safety awareness and education for a safer tomorrow through community engagement and innovative programs.">
    <!-- Hero Slider -->
    @php
    $heroSlides = [
        [
            'title' => 'Building Safer Roads for <span class="text-safety-yellow">Everyone</span>',
            'subtitle' => 'Vibrant Road Safety Awareness Foundation',
            'description' => 'Join us in our mission to create awareness, educate communities, and build a culture of road safety that saves lives and protects families across our nation.',
            'badge' => '🚗 Road Safety Foundation',
            'actions' => [
                [
                    'text' => 'Get Involved Today',
                    'url' => route('volunteer.index'),
                    'style' => 'bg-safety-yellow text-black hover:bg-safety-yellow/90 shadow-xl',
                    'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/></svg>'
                ],
                [
                    'text' => 'Learn More',
                    'url' => route('about.index'),
                    'style' => 'bg-white/10 backdrop-blur-sm border border-white/30 text-white hover:bg-white/20',
                    'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/></svg>'
                ]
            ]
        ],
        [
            'title' => 'Educating Communities for <span class="text-safety-green">Safer Tomorrow</span>',
            'subtitle' => 'Comprehensive Training Programs',
            'description' => 'Through innovative training programs and community engagement, we\'re building awareness that prevents accidents and saves lives on our roads.',
            'badge' => '📚 Education & Training',
            'actions' => [
                [
                    'text' => 'View Training Programs',
                    'url' => route('training.index'),
                    'style' => 'bg-safety-green text-white hover:bg-safety-green/90 shadow-xl',
                    'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/></svg>'
                ],
                [
                    'text' => 'Our Impact',
                    'url' => '#impact',
                    'style' => 'bg-white/10 backdrop-blur-sm border border-white/30 text-white hover:bg-white/20',
                    'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/></svg>'
                ]
            ]
        ],
        [
            'title' => 'Making Roads Safer Through <span class="text-safety-orange">Innovation</span>',
            'subtitle' => 'Technology & Advocacy',
            'description' => 'Leveraging cutting-edge technology and evidence-based advocacy to create lasting change in road safety policies and practices.',
            'badge' => '🚀 Innovation & Technology',
            'actions' => [
                [
                    'text' => 'Explore Our Works',
                    'url' => route('works.index'),
                    'style' => 'bg-safety-orange text-white hover:bg-safety-orange/90 shadow-xl',
                    'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/></svg>'
                ],
                [
                    'text' => 'Contact Us',
                    'url' => route('contact.index'),
                    'style' => 'bg-white/10 backdrop-blur-sm border border-white/30 text-white hover:bg-white/20',
                    'icon' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/></svg>'
                ]
            ]
        ]
    ];
    @endphp

    <x-hero-slider :slides="$heroSlides" :autoplay="true" :interval="6000" />

    <!-- Key Statistics Section -->
    @php
    $impactStats = [
        [
            'value' => '50,000+',
            'label' => 'Lives Educated',
            'description' => 'People reached through our safety programs',
            'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/></svg>'
        ],
        [
            'value' => '200+',
            'label' => 'Training Sessions',
            'description' => 'Conducted across communities',
            'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/></svg>'
        ],
        [
            'value' => '85%',
            'label' => 'Accident Reduction',
            'description' => 'In areas with our programs',
            'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/></svg>'
        ],
        [
            'value' => '1,500+',
            'label' => 'Active Volunteers',
            'description' => 'Dedicated community champions',
            'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/></svg>'
        ]
    ];
    @endphp

    <x-section id="impact" padding="lg">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Our Impact in Numbers</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Through dedicated efforts and community partnerships, we're making measurable progress in road safety awareness.
            </p>
        </div>

        <x-stats :stats="$impactStats" />
    </x-section>

    <!-- Mission Highlight Section -->
    <x-section background="muted" padding="lg">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div class="order-2 lg:order-1">
                <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-primary/15 to-safety-green/15 text-primary border border-primary/30 mb-8 backdrop-blur-sm">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>
                    Our Mission
                </div>
                <h2 class="text-4xl md:text-5xl font-bold text-foreground mb-8 leading-tight">
                    Creating a Culture of <span class="bg-gradient-to-r from-primary to-safety-green bg-clip-text text-transparent">Road Safety</span>
                </h2>
                <p class="text-xl text-muted-foreground mb-10 leading-relaxed">
                    We believe that every life lost on our roads is preventable. Through comprehensive education,
                    community engagement, and innovative training programs, we're building a safer future for all road users.
                </p>
                <div class="space-y-6 mb-10">
                    <div class="flex items-start space-x-4 group">
                        <div class="w-10 h-10 bg-gradient-to-br from-success to-safety-green rounded-xl flex items-center justify-center flex-shrink-0 mt-1 group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-foreground mb-2">Community Education</h3>
                            <p class="text-muted-foreground leading-relaxed">Comprehensive safety programs designed for all age groups, from children to seniors</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-4 group">
                        <div class="w-10 h-10 bg-gradient-to-br from-primary to-safety-orange rounded-xl flex items-center justify-center flex-shrink-0 mt-1 group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-foreground mb-2">Professional Training</h3>
                            <p class="text-muted-foreground leading-relaxed">Capacity building programs for drivers and transport professionals</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-4 group">
                        <div class="w-10 h-10 bg-gradient-to-br from-safety-yellow to-safety-orange rounded-xl flex items-center justify-center flex-shrink-0 mt-1 group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-bold text-foreground mb-2">Policy Advocacy</h3>
                            <p class="text-muted-foreground leading-relaxed">Working with authorities to develop better road safety policies and regulations</p>
                        </div>
                    </div>
                </div>
                <x-button href="{{ route('about.index') }}" size="lg" class="bg-gradient-to-r from-primary to-safety-green hover:from-primary/90 hover:to-safety-green/90 shadow-lg hover:shadow-xl transition-all duration-300">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Learn About Our Mission
                </x-button>
            </div>
            <div class="relative order-1 lg:order-2">
                <div class="aspect-square bg-gradient-to-br from-primary/10 via-safety-green/5 to-safety-yellow/10 rounded-3xl p-12 flex items-center justify-center relative overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="absolute inset-0 opacity-5">
                        <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <pattern id="mission-grid" width="10" height="10" patternUnits="userSpaceOnUse">
                                    <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" stroke-width="0.5"/>
                                </pattern>
                            </defs>
                            <rect width="100" height="100" fill="url(#mission-grid)" />
                        </svg>
                    </div>

                    <div class="w-full h-full bg-gradient-to-br from-primary/20 to-safety-green/20 rounded-2xl flex items-center justify-center backdrop-blur-sm border border-white/20 shadow-2xl">
                        <svg class="w-40 h-40 text-primary drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                        </svg>
                    </div>
                </div>
                <!-- Enhanced Decorative elements -->
                <div class="absolute -top-6 -right-6 w-32 h-32 bg-gradient-to-br from-safety-yellow/30 to-safety-orange/20 rounded-full blur-xl"></div>
                <div class="absolute -bottom-6 -left-6 w-24 h-24 bg-gradient-to-br from-safety-green/30 to-primary/20 rounded-full blur-xl"></div>
                <div class="absolute top-1/4 -right-4 w-16 h-16 bg-primary/20 rounded-full"></div>
                <div class="absolute bottom-1/4 -left-4 w-12 h-12 bg-safety-yellow/30 rounded-full"></div>
            </div>
        </div>
    </x-section>

    <!-- Upcoming Events Preview -->
    <x-section padding="lg">
        <div class="text-center mb-16">
            <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-safety-orange/15 to-safety-yellow/15 text-safety-orange border border-safety-orange/30 mb-6 backdrop-blur-sm">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>
                Upcoming Events
            </div>
            <h2 class="text-4xl md:text-5xl font-bold text-foreground mb-6">Join Our <span class="bg-gradient-to-r from-safety-orange to-safety-yellow bg-clip-text text-transparent">Initiatives</span></h2>
            <p class="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                Be part of the road safety movement and make a real difference in your community through our engaging events and programs.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            @php
            $events = [
                [
                    'title' => 'Road Safety Workshop',
                    'date' => 'March 15, 2024',
                    'time' => '10:00 AM - 4:00 PM',
                    'location' => 'Community Center, Downtown',
                    'type' => 'Workshop',
                    'description' => 'Comprehensive training on defensive driving and road safety awareness for community members.'
                ],
                [
                    'title' => 'School Safety Campaign',
                    'date' => 'March 22, 2024',
                    'time' => '9:00 AM - 12:00 PM',
                    'location' => 'Lincoln Elementary School',
                    'type' => 'Campaign',
                    'description' => 'Interactive safety education program designed specifically for school children and teachers.'
                ],
                [
                    'title' => 'Driver Training Certification',
                    'date' => 'March 28, 2024',
                    'time' => '8:00 AM - 5:00 PM',
                    'location' => 'Training Center',
                    'type' => 'Training',
                    'description' => 'Professional certification program for commercial drivers and transport operators.'
                ]
            ];
            @endphp
            
            @foreach($events as $index => $event)
                @php
                $gradients = [
                    'from-primary/10 to-safety-green/10 border-primary/20',
                    'from-safety-orange/10 to-safety-yellow/10 border-safety-orange/20',
                    'from-safety-green/10 to-primary/10 border-safety-green/20'
                ];
                $iconColors = ['text-primary', 'text-safety-orange', 'text-safety-green'];
                $currentGradient = $gradients[$index % 3];
                $currentIconColor = $iconColors[$index % 3];
                @endphp

                <x-card hover="true" class="group relative overflow-hidden bg-gradient-to-br {{ $currentGradient }} border backdrop-blur-sm">
                    <!-- Background decoration -->
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-white/5 to-transparent rounded-full -translate-y-16 translate-x-16"></div>

                    <div class="relative z-10">
                        <div class="flex items-center justify-between mb-6">
                            <span class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold bg-white/80 backdrop-blur-sm {{ $currentIconColor }} border border-current/20">
                                {{ $event['type'] }}
                            </span>
                            <div class="w-10 h-10 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-5 h-5 {{ $currentIconColor }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                            </div>
                        </div>

                        <h3 class="text-xl font-bold text-foreground mb-3 group-hover:{{ $currentIconColor }} transition-colors duration-300">
                            {{ $event['title'] }}
                        </h3>

                        <p class="text-muted-foreground text-sm mb-6 leading-relaxed">
                            {{ $event['description'] }}
                        </p>

                        <div class="space-y-3">
                            <div class="flex items-center space-x-3 text-sm text-muted-foreground">
                                <div class="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                    </svg>
                                </div>
                                <span class="font-medium">{{ $event['date'] }}</span>
                            </div>
                            <div class="flex items-center space-x-3 text-sm text-muted-foreground">
                                <div class="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <span class="font-medium">{{ $event['time'] }}</span>
                            </div>
                            <div class="flex items-center space-x-3 text-sm text-muted-foreground">
                                <div class="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                    </svg>
                                </div>
                                <span class="font-medium">{{ $event['location'] }}</span>
                            </div>
                        </div>
                    </div>
                </x-card>
            @endforeach
        </div>
        
        <div class="text-center mt-16">
            <x-button href="#" variant="outline" size="lg" class="bg-gradient-to-r from-transparent to-transparent hover:from-primary/5 hover:to-safety-orange/5 border-2 border-primary/30 hover:border-primary/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-300">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>
                View All Events
                <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                </svg>
            </x-button>
        </div>
    </x-section>

    <!-- Blog & Media Preview -->
    <x-section background="muted" padding="lg">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
            <!-- Latest Blog Posts -->
            <div>
                <div class="flex items-center justify-between mb-10">
                    <div>
                        <div class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold bg-gradient-to-r from-primary/15 to-safety-green/15 text-primary border border-primary/30 mb-3 backdrop-blur-sm">
                            <svg class="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            Blog Posts
                        </div>
                        <h2 class="text-3xl md:text-4xl font-bold text-foreground">Latest <span class="bg-gradient-to-r from-primary to-safety-green bg-clip-text text-transparent">Insights</span></h2>
                    </div>
                    <x-button href="{{ route('blog.index') }}" variant="ghost" size="sm" class="hover:bg-primary/10 hover:text-primary transition-all duration-300">
                        View All
                        <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </x-button>
                </div>

                <div class="space-y-6">
                    @php
                    $blogPosts = [
                        [
                            'title' => '10 Essential Road Safety Tips Every Driver Should Know',
                            'excerpt' => 'Discover the fundamental safety practices that can prevent accidents and save lives on our roads.',
                            'date' => 'March 10, 2024',
                            'author' => 'Dr. Sarah Johnson',
                            'readTime' => '5 min read',
                            'category' => 'Safety Tips'
                        ],
                        [
                            'title' => 'The Impact of Mobile Phones on Road Safety',
                            'excerpt' => 'Understanding the dangers of distracted driving and how to stay focused behind the wheel.',
                            'date' => 'March 8, 2024',
                            'author' => 'Michael Chen',
                            'readTime' => '7 min read',
                            'category' => 'Research'
                        ],
                        [
                            'title' => 'Community Success Story: Reducing Accidents by 40%',
                            'excerpt' => 'How one community transformed their road safety culture through our comprehensive program.',
                            'date' => 'March 5, 2024',
                            'author' => 'Lisa Rodriguez',
                            'readTime' => '4 min read',
                            'category' => 'Success Stories'
                        ]
                    ];
                    @endphp

                    @foreach($blogPosts as $index => $post)
                        @php
                        $blogGradients = [
                            'from-primary/15 to-safety-green/10',
                            'from-safety-orange/15 to-safety-yellow/10',
                            'from-safety-green/15 to-primary/10'
                        ];
                        $blogIconColors = ['text-primary', 'text-safety-orange', 'text-safety-green'];
                        $currentBlogGradient = $blogGradients[$index % 3];
                        $currentBlogIconColor = $blogIconColors[$index % 3];
                        @endphp

                        <article class="group cursor-pointer p-6 rounded-2xl bg-gradient-to-br {{ $currentBlogGradient }} border border-white/20 backdrop-blur-sm hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                            <div class="flex space-x-5">
                                <div class="w-16 h-16 bg-white/80 backdrop-blur-sm rounded-2xl flex-shrink-0 flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-sm">
                                    <svg class="w-7 h-7 {{ $currentBlogIconColor }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-3 mb-3">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-white/80 backdrop-blur-sm {{ $currentBlogIconColor }} border border-current/20">
                                            {{ $post['category'] }}
                                        </span>
                                        <span class="text-xs text-muted-foreground font-medium bg-white/50 px-2 py-1 rounded-full">{{ $post['readTime'] }}</span>
                                    </div>
                                    <h3 class="text-lg font-bold text-foreground group-hover:{{ $currentBlogIconColor }} transition-colors duration-300 line-clamp-2 mb-3 leading-tight">
                                        {{ $post['title'] }}
                                    </h3>
                                    <p class="text-sm text-muted-foreground line-clamp-2 mb-4 leading-relaxed">
                                        {{ $post['excerpt'] }}
                                    </p>
                                    <div class="flex items-center text-xs text-muted-foreground">
                                        <div class="flex items-center space-x-2">
                                            <div class="w-6 h-6 bg-white/60 rounded-full flex items-center justify-center">
                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                                </svg>
                                            </div>
                                            <span class="font-medium">{{ $post['author'] }}</span>
                                        </div>
                                        <span class="mx-3">•</span>
                                        <span>{{ $post['date'] }}</span>
                                    </div>
                                </div>
                            </div>
                        </article>
                    @endforeach
                </div>
            </div>

            <!-- Media Coverage -->
            <div>
                <div class="flex items-center justify-between mb-10">
                    <div>
                        <div class="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold bg-gradient-to-r from-safety-orange/15 to-safety-yellow/15 text-safety-orange border border-safety-orange/30 mb-3 backdrop-blur-sm">
                            <svg class="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                            </svg>
                            Media Coverage
                        </div>
                        <h2 class="text-3xl md:text-4xl font-bold text-foreground">In the <span class="bg-gradient-to-r from-safety-orange to-safety-yellow bg-clip-text text-transparent">Spotlight</span></h2>
                    </div>
                    <x-button href="{{ route('media.coverage') }}" variant="ghost" size="sm" class="hover:bg-safety-orange/10 hover:text-safety-orange transition-all duration-300">
                        View All
                        <svg class="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                        </svg>
                    </x-button>
                </div>

                <div class="space-y-6">
                    @php
                    $mediaItems = [
                        [
                            'title' => 'Local Foundation Launches Major Road Safety Initiative',
                            'outlet' => 'City News Network',
                            'date' => 'March 12, 2024',
                            'type' => 'News Article',
                            'description' => 'Comprehensive coverage of our latest community outreach program and its expected impact.'
                        ],
                        [
                            'title' => 'Interview: Road Safety Expert Discusses Prevention Strategies',
                            'outlet' => 'Morning Radio Show',
                            'date' => 'March 9, 2024',
                            'type' => 'Radio Interview',
                            'description' => 'Our director shares insights on effective road safety education and community engagement.'
                        ],
                        [
                            'title' => 'Documentary Feature: Changing Lives Through Safety Education',
                            'outlet' => 'Public Television',
                            'date' => 'March 6, 2024',
                            'type' => 'TV Feature',
                            'description' => 'A 30-minute documentary showcasing real stories of lives saved through our programs.'
                        ]
                    ];
                    @endphp

                    @foreach($mediaItems as $index => $item)
                        @php
                        $mediaGradients = [
                            'from-safety-orange/15 to-safety-yellow/10',
                            'from-safety-yellow/15 to-safety-green/10',
                            'from-safety-green/15 to-safety-orange/10'
                        ];
                        $mediaIconColors = ['text-safety-orange', 'text-safety-yellow', 'text-safety-green'];
                        $currentMediaGradient = $mediaGradients[$index % 3];
                        $currentMediaIconColor = $mediaIconColors[$index % 3];
                        @endphp

                        <article class="group cursor-pointer p-6 rounded-2xl bg-gradient-to-br {{ $currentMediaGradient }} border border-white/20 backdrop-blur-sm hover:shadow-lg transition-all duration-300 hover:scale-[1.02]">
                            <div class="flex space-x-5">
                                <div class="w-16 h-16 bg-white/80 backdrop-blur-sm rounded-2xl flex-shrink-0 flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-sm">
                                    <svg class="w-7 h-7 {{ $currentMediaIconColor }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                    </svg>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-2 mb-3">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-white/80 backdrop-blur-sm {{ $currentMediaIconColor }} border border-current/20">
                                            {{ $item['type'] }}
                                        </span>
                                    </div>
                                    <h3 class="text-lg font-bold text-foreground group-hover:{{ $currentMediaIconColor }} transition-colors duration-300 line-clamp-2 mb-3 leading-tight">
                                        {{ $item['title'] }}
                                    </h3>
                                    <p class="text-sm text-muted-foreground line-clamp-2 mb-4 leading-relaxed">
                                        {{ $item['description'] }}
                                    </p>
                                    <div class="flex items-center text-xs text-muted-foreground">
                                        <div class="flex items-center space-x-2">
                                            <div class="w-6 h-6 bg-white/60 rounded-full flex items-center justify-center">
                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"/>
                                                </svg>
                                            </div>
                                            <span class="font-bold">{{ $item['outlet'] }}</span>
                                        </div>
                                        <span class="mx-3">•</span>
                                        <span>{{ $item['date'] }}</span>
                                    </div>
                                </div>
                            </div>
                        </article>
                    @endforeach
                </div>
            </div>
        </div>
    </x-section>

    <!-- Call to Action Section -->
    <x-section padding="lg" class="relative overflow-hidden">
        <!-- Background decoration -->
        <div class="absolute inset-0 bg-gradient-to-br from-primary/5 via-safety-green/5 to-safety-yellow/5"></div>
        <div class="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-br from-primary/10 to-transparent rounded-full blur-3xl"></div>
        <div class="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-br from-safety-green/10 to-transparent rounded-full blur-3xl"></div>

        <div class="relative z-10 text-center">
            <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r from-safety-green/15 to-primary/15 text-safety-green border border-safety-green/30 mb-8 backdrop-blur-sm">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                </svg>
                Get Involved
            </div>

            <h2 class="text-4xl md:text-6xl font-bold text-foreground mb-6 leading-tight">
                Ready to Make a <span class="bg-gradient-to-r from-safety-green to-primary bg-clip-text text-transparent">Difference?</span>
            </h2>

            <p class="text-xl text-muted-foreground max-w-3xl mx-auto mb-12 leading-relaxed">
                Join thousands of volunteers and community members who are actively working to make our roads safer for everyone. Your contribution can save lives.
            </p>

            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <x-button href="{{ route('volunteer.index') }}" size="lg" class="bg-gradient-to-r from-safety-green to-primary hover:from-safety-green/90 hover:to-primary/90 shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 group">
                    <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                    </svg>
                    Become a Volunteer
                    <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </x-button>

                <x-button href="{{ route('contact.index') }}" variant="outline" size="lg" class="border-2 border-primary/30 hover:border-primary/50 bg-white/50 backdrop-blur-sm hover:bg-white/70 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 group">
                    <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                    </svg>
                    Get in Touch
                    <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </x-button>
            </div>
        </div>
    </x-section>
</x-app-layout>
