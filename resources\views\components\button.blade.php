@props([
    'variant' => 'primary', // primary, secondary, outline, ghost, destructive, success, warning
    'size' => 'default', // sm, default, lg, xl
    'href' => null,
    'type' => 'button',
    'disabled' => false,
    'loading' => false,
    'icon' => null,
    'iconPosition' => 'left' // left, right
])

@php
$baseClasses = 'inline-flex items-center justify-center rounded-xl font-semibold transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 transform hover:scale-105 active:scale-95 hover:-translate-y-0.5';

$variantClasses = match($variant) {
    'secondary' => 'bg-secondary text-secondary-foreground hover:bg-secondary-hover shadow-soft hover:shadow-medium border border-border/50',
    'outline' => 'border-2 border-border bg-background hover:bg-primary/5 hover:text-primary hover:border-primary/30 shadow-soft hover:shadow-medium',
    'ghost' => 'hover:bg-primary/8 hover:text-primary transition-colors duration-200',
    'destructive' => 'bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-soft hover:shadow-medium',
    'success' => 'bg-success text-success-foreground hover:bg-success/90 shadow-soft hover:shadow-medium',
    'warning' => 'bg-warning text-warning-foreground hover:bg-warning/90 shadow-soft hover:shadow-medium',
    default => 'bg-gradient-to-r from-primary to-primary-hover text-primary-foreground hover:from-primary-hover hover:to-primary shadow-soft hover:shadow-medium'
};

$sizeClasses = match($size) {
    'sm' => 'h-9 px-3 text-sm',
    'lg' => 'h-11 px-8 text-base',
    'xl' => 'h-12 px-10 text-lg',
    default => 'h-10 px-4 py-2'
};

$classes = trim("{$baseClasses} {$variantClasses} {$sizeClasses}");

$iconClasses = match($size) {
    'sm' => 'w-4 h-4',
    'lg' => 'w-6 h-6',
    'xl' => 'w-7 h-7',
    default => 'w-5 h-5'
};
@endphp

@if($href)
    <a href="{{ $href }}" {{ $attributes->merge(['class' => $classes]) }}>
        @if($loading)
            <svg class="animate-spin -ml-1 mr-3 {{ $iconClasses }}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        @elseif($icon && $iconPosition === 'left')
            <span class="mr-2 {{ $iconClasses }}">{!! $icon !!}</span>
        @endif
        
        {{ $slot }}
        
        @if($icon && $iconPosition === 'right')
            <span class="ml-2 {{ $iconClasses }}">{!! $icon !!}</span>
        @endif
    </a>
@else
    <button 
        type="{{ $type }}" 
        {{ $disabled ? 'disabled' : '' }}
        {{ $attributes->merge(['class' => $classes]) }}
    >
        @if($loading)
            <svg class="animate-spin -ml-1 mr-3 {{ $iconClasses }}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
        @elseif($icon && $iconPosition === 'left')
            <span class="mr-2 {{ $iconClasses }}">{!! $icon !!}</span>
        @endif
        
        {{ $slot }}
        
        @if($icon && $iconPosition === 'right')
            <span class="ml-2 {{ $iconClasses }}">{!! $icon !!}</span>
        @endif
    </button>
@endif
