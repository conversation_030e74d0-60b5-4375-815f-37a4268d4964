<?php if (isset($component)) { $__componentOriginal4619374cef299e94fd7263111d0abc69 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4619374cef299e94fd7263111d0abc69 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.app-layout','data' => ['title' => 'Become a Volunteer - Vibrant Road Safety Awareness Foundation','description' => 'Join our community of dedicated volunteers and help make roads safer for everyone. Discover volunteer opportunities and how you can make a difference.']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Become a Volunteer - Vibrant Road Safety Awareness Foundation','description' => 'Join our community of dedicated volunteers and help make roads safer for everyone. Discover volunteer opportunities and how you can make a difference.']); ?>
    <!-- Hero Section -->
    <?php if (isset($component)) { $__componentOriginal04f02f1e0f152287a127192de01fe241 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal04f02f1e0f152287a127192de01fe241 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.hero','data' => ['title' => 'Make a <span class=\'text-safety-yellow\'>Difference</span>','subtitle' => 'Become a Volunteer','description' => 'Join our community of passionate volunteers who are dedicated to making roads safer for everyone. Your time and skills can help save lives and strengthen communities.','size' => 'lg']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('hero'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Make a <span class=\'text-safety-yellow\'>Difference</span>','subtitle' => 'Become a Volunteer','description' => 'Join our community of passionate volunteers who are dedicated to making roads safer for everyone. Your time and skills can help save lives and strengthen communities.','size' => 'lg']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal04f02f1e0f152287a127192de01fe241)): ?>
<?php $attributes = $__attributesOriginal04f02f1e0f152287a127192de01fe241; ?>
<?php unset($__attributesOriginal04f02f1e0f152287a127192de01fe241); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal04f02f1e0f152287a127192de01fe241)): ?>
<?php $component = $__componentOriginal04f02f1e0f152287a127192de01fe241; ?>
<?php unset($__componentOriginal04f02f1e0f152287a127192de01fe241); ?>
<?php endif; ?>

    <!-- Why Volunteer Section -->
    <?php if (isset($component)) { $__componentOriginal785c8021fd1a6e19eb80cad4b837cda0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal785c8021fd1a6e19eb80cad4b837cda0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.section','data' => ['padding' => 'lg']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['padding' => 'lg']); ?>
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Why Volunteer With Us?</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Volunteering with VRSAF offers meaningful opportunities to contribute to road safety while developing new skills and connecting with like-minded individuals.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php
            $benefits = [
                [
                    'title' => 'Make a Real Impact',
                    'description' => 'Your efforts directly contribute to saving lives and preventing accidents in your community.',
                    'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/></svg>',
                    'color' => 'safety-red'
                ],
                [
                    'title' => 'Develop New Skills',
                    'description' => 'Gain valuable experience in education, public speaking, event management, and community outreach.',
                    'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/></svg>',
                    'color' => 'safety-yellow'
                ],
                [
                    'title' => 'Build Community',
                    'description' => 'Connect with passionate individuals who share your commitment to road safety and community service.',
                    'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/></svg>',
                    'color' => 'safety-green'
                ],
                [
                    'title' => 'Flexible Commitment',
                    'description' => 'Choose volunteer opportunities that fit your schedule and availability, from one-time events to ongoing programs.',
                    'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/></svg>',
                    'color' => 'primary'
                ],
                [
                    'title' => 'Professional Growth',
                    'description' => 'Enhance your resume with meaningful volunteer experience and develop leadership capabilities.',
                    'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/></svg>',
                    'color' => 'safety-orange'
                ],
                [
                    'title' => 'Recognition & Rewards',
                    'description' => 'Receive certificates, awards, and recognition for your contributions to road safety advocacy.',
                    'icon' => '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"/></svg>',
                    'color' => 'secondary'
                ]
            ];
            ?>
            
            <?php $__currentLoopData = $benefits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $benefit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="text-center group">
                    <div class="w-16 h-16 bg-<?php echo e($benefit['color']); ?>/10 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-<?php echo e($benefit['color']); ?>/20 transition-smooth">
                        <div class="w-8 h-8 text-<?php echo e($benefit['color']); ?>">
                            <?php echo $benefit['icon']; ?>

                        </div>
                    </div>
                    <h3 class="text-lg font-semibold text-foreground mb-2"><?php echo e($benefit['title']); ?></h3>
                    <p class="text-sm text-muted-foreground leading-relaxed"><?php echo e($benefit['description']); ?></p>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal785c8021fd1a6e19eb80cad4b837cda0)): ?>
<?php $attributes = $__attributesOriginal785c8021fd1a6e19eb80cad4b837cda0; ?>
<?php unset($__attributesOriginal785c8021fd1a6e19eb80cad4b837cda0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal785c8021fd1a6e19eb80cad4b837cda0)): ?>
<?php $component = $__componentOriginal785c8021fd1a6e19eb80cad4b837cda0; ?>
<?php unset($__componentOriginal785c8021fd1a6e19eb80cad4b837cda0); ?>
<?php endif; ?>

    <!-- Volunteer Opportunities -->
    <?php if (isset($component)) { $__componentOriginal785c8021fd1a6e19eb80cad4b837cda0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal785c8021fd1a6e19eb80cad4b837cda0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.section','data' => ['background' => 'muted','padding' => 'lg']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['background' => 'muted','padding' => 'lg']); ?>
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Volunteer Opportunities</h2>
            <p class="text-lg text-muted-foreground max-w-2xl mx-auto">
                Find the perfect volunteer role that matches your interests, skills, and availability.
            </p>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <?php
            $opportunities = [
                [
                    'title' => 'Community Safety Educator',
                    'commitment' => '4-6 hours/month',
                    'location' => 'Various community locations',
                    'description' => 'Deliver safety presentations to schools, community groups, and organizations.',
                    'requirements' => [
                        'Comfortable with public speaking',
                        'Reliable transportation',
                        'Complete training program',
                        'Background check required'
                    ],
                    'benefits' => [
                        'Comprehensive training provided',
                        'Flexible scheduling',
                        'Presentation materials supplied',
                        'Ongoing support and mentorship'
                    ]
                ],
                [
                    'title' => 'Event Support Volunteer',
                    'commitment' => '2-4 hours per event',
                    'location' => 'Event venues across the region',
                    'description' => 'Assist with setup, registration, and coordination at safety events and workshops.',
                    'requirements' => [
                        'Friendly and outgoing personality',
                        'Ability to stand for extended periods',
                        'Team player attitude',
                        'Punctual and reliable'
                    ],
                    'benefits' => [
                        'Meet diverse community members',
                        'Learn about event management',
                        'Flexible event selection',
                        'Free event materials and refreshments'
                    ]
                ],
                [
                    'title' => 'Digital Content Creator',
                    'commitment' => '3-5 hours/week',
                    'location' => 'Remote/Home-based',
                    'description' => 'Create social media content, blog posts, and digital materials for safety campaigns.',
                    'requirements' => [
                        'Strong writing or design skills',
                        'Social media experience',
                        'Creative mindset',
                        'Basic computer skills'
                    ],
                    'benefits' => [
                        'Work from home flexibility',
                        'Build digital portfolio',
                        'Creative freedom',
                        'Professional development opportunities'
                    ]
                ],
                [
                    'title' => 'Administrative Support',
                    'commitment' => '2-3 hours/week',
                    'location' => 'Foundation office or remote',
                    'description' => 'Provide administrative support including data entry, filing, and general office tasks.',
                    'requirements' => [
                        'Basic computer skills',
                        'Attention to detail',
                        'Organizational skills',
                        'Professional communication'
                    ],
                    'benefits' => [
                        'Office experience',
                        'Flexible hours',
                        'Professional references',
                        'Skill development opportunities'
                    ]
                ]
            ];
            ?>
            
            <?php $__currentLoopData = $opportunities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $opportunity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if (isset($component)) { $__componentOriginal53747ceb358d30c0105769f8471417f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53747ceb358d30c0105769f8471417f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card','data' => ['variant' => 'elevated','hover' => 'true','class' => 'group']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'elevated','hover' => 'true','class' => 'group']); ?>
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-bold text-foreground group-hover:text-primary transition-smooth">
                            <?php echo e($opportunity['title']); ?>

                        </h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
                            <?php echo e($opportunity['commitment']); ?>

                        </span>
                    </div>
                    
                    <div class="flex items-center text-sm text-muted-foreground mb-4">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                        </svg>
                        <?php echo e($opportunity['location']); ?>

                    </div>
                    
                    <p class="text-muted-foreground mb-6 leading-relaxed">
                        <?php echo e($opportunity['description']); ?>

                    </p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-foreground mb-3">Requirements:</h4>
                            <ul class="space-y-2">
                                <?php $__currentLoopData = $opportunity['requirements']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $requirement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="flex items-start space-x-2">
                                        <svg class="w-4 h-4 text-primary mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"/>
                                        </svg>
                                        <span class="text-sm text-muted-foreground"><?php echo e($requirement); ?></span>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                        
                        <div>
                            <h4 class="font-semibold text-foreground mb-3">Benefits:</h4>
                            <ul class="space-y-2">
                                <?php $__currentLoopData = $opportunity['benefits']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $benefit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="flex items-start space-x-2">
                                        <svg class="w-4 h-4 text-success mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        <span class="text-sm text-muted-foreground"><?php echo e($benefit); ?></span>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-6 pt-6 border-t border-border">
                        <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['href' => '#volunteer-form','size' => 'sm','class' => 'w-full']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => '#volunteer-form','size' => 'sm','class' => 'w-full']); ?>
                            Apply for This Role
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
                    </div>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $attributes = $__attributesOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__attributesOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $component = $__componentOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__componentOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal785c8021fd1a6e19eb80cad4b837cda0)): ?>
<?php $attributes = $__attributesOriginal785c8021fd1a6e19eb80cad4b837cda0; ?>
<?php unset($__attributesOriginal785c8021fd1a6e19eb80cad4b837cda0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal785c8021fd1a6e19eb80cad4b837cda0)): ?>
<?php $component = $__componentOriginal785c8021fd1a6e19eb80cad4b837cda0; ?>
<?php unset($__componentOriginal785c8021fd1a6e19eb80cad4b837cda0); ?>
<?php endif; ?>

    <!-- Volunteer Application Form -->
    <?php if (isset($component)) { $__componentOriginal785c8021fd1a6e19eb80cad4b837cda0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal785c8021fd1a6e19eb80cad4b837cda0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.section','data' => ['padding' => 'lg','id' => 'volunteer-form']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['padding' => 'lg','id' => 'volunteer-form']); ?>
        <div class="max-w-3xl mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-foreground mb-4">Join Our Team</h2>
                <p class="text-lg text-muted-foreground">
                    Ready to make a difference? Fill out the form below to start your volunteer journey with us.
                </p>
            </div>
            
            <?php if (isset($component)) { $__componentOriginal53747ceb358d30c0105769f8471417f6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal53747ceb358d30c0105769f8471417f6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.card','data' => ['variant' => 'elevated']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['variant' => 'elevated']); ?>
                <form class="space-y-6" id="volunteer-application-form">
                    <!-- Personal Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="first_name" class="block text-sm font-medium text-foreground mb-2">First Name *</label>
                            <input type="text" id="first_name" name="first_name" required class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                        </div>
                        <div>
                            <label for="last_name" class="block text-sm font-medium text-foreground mb-2">Last Name *</label>
                            <input type="text" id="last_name" name="last_name" required class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="email" class="block text-sm font-medium text-foreground mb-2">Email Address *</label>
                            <input type="email" id="email" name="email" required class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                        </div>
                        <div>
                            <label for="phone" class="block text-sm font-medium text-foreground mb-2">Phone Number *</label>
                            <input type="tel" id="phone" name="phone" required class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                        </div>
                    </div>
                    
                    <div>
                        <label for="address" class="block text-sm font-medium text-foreground mb-2">Address</label>
                        <textarea id="address" name="address" rows="2" class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"></textarea>
                    </div>
                    
                    <!-- Volunteer Preferences -->
                    <div>
                        <label class="block text-sm font-medium text-foreground mb-3">Volunteer Opportunities of Interest *</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <label class="flex items-center">
                                <input type="checkbox" name="opportunities[]" value="educator" class="rounded border-input text-primary focus:ring-ring">
                                <span class="ml-2 text-sm text-foreground">Community Safety Educator</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="opportunities[]" value="event_support" class="rounded border-input text-primary focus:ring-ring">
                                <span class="ml-2 text-sm text-foreground">Event Support Volunteer</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="opportunities[]" value="content_creator" class="rounded border-input text-primary focus:ring-ring">
                                <span class="ml-2 text-sm text-foreground">Digital Content Creator</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="opportunities[]" value="admin_support" class="rounded border-input text-primary focus:ring-ring">
                                <span class="ml-2 text-sm text-foreground">Administrative Support</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="availability" class="block text-sm font-medium text-foreground mb-2">Availability *</label>
                            <select id="availability" name="availability" required class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                                <option value="">Select availability</option>
                                <option value="weekdays">Weekdays</option>
                                <option value="weekends">Weekends</option>
                                <option value="both">Both weekdays and weekends</option>
                                <option value="flexible">Flexible</option>
                            </select>
                        </div>
                        <div>
                            <label for="commitment" class="block text-sm font-medium text-foreground mb-2">Time Commitment *</label>
                            <select id="commitment" name="commitment" required class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent">
                                <option value="">Select commitment level</option>
                                <option value="1-2_hours">1-2 hours per week</option>
                                <option value="3-5_hours">3-5 hours per week</option>
                                <option value="6-10_hours">6-10 hours per week</option>
                                <option value="events_only">Events only</option>
                            </select>
                        </div>
                    </div>
                    
                    <div>
                        <label for="experience" class="block text-sm font-medium text-foreground mb-2">Relevant Experience or Skills</label>
                        <textarea id="experience" name="experience" rows="4" placeholder="Tell us about any relevant experience, skills, or qualifications you have..." class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"></textarea>
                    </div>
                    
                    <div>
                        <label for="motivation" class="block text-sm font-medium text-foreground mb-2">Why do you want to volunteer with us? *</label>
                        <textarea id="motivation" name="motivation" rows="4" required placeholder="Share your motivation for volunteering with the Vibrant Road Safety Awareness Foundation..." class="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"></textarea>
                    </div>
                    
                    <!-- Agreement -->
                    <div class="space-y-3">
                        <label class="flex items-start">
                            <input type="checkbox" name="background_check" required class="mt-1 rounded border-input text-primary focus:ring-ring">
                            <span class="ml-2 text-sm text-muted-foreground">I understand that a background check may be required for certain volunteer positions *</span>
                        </label>
                        <label class="flex items-start">
                            <input type="checkbox" name="terms" required class="mt-1 rounded border-input text-primary focus:ring-ring">
                            <span class="ml-2 text-sm text-muted-foreground">I agree to the volunteer terms and conditions and privacy policy *</span>
                        </label>
                        <label class="flex items-start">
                            <input type="checkbox" name="newsletter" class="mt-1 rounded border-input text-primary focus:ring-ring">
                            <span class="ml-2 text-sm text-muted-foreground">I would like to receive updates about volunteer opportunities and foundation news</span>
                        </label>
                    </div>
                    
                    <div class="pt-6">
                        <?php if (isset($component)) { $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.button','data' => ['type' => 'submit','size' => 'lg','class' => 'w-full']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'submit','size' => 'lg','class' => 'w-full']); ?>
                            Submit Application
                            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                            </svg>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $attributes = $__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__attributesOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561)): ?>
<?php $component = $__componentOriginald0f1fd2689e4bb7060122a5b91fe8561; ?>
<?php unset($__componentOriginald0f1fd2689e4bb7060122a5b91fe8561); ?>
<?php endif; ?>
                    </div>
                </form>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $attributes = $__attributesOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__attributesOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal53747ceb358d30c0105769f8471417f6)): ?>
<?php $component = $__componentOriginal53747ceb358d30c0105769f8471417f6; ?>
<?php unset($__componentOriginal53747ceb358d30c0105769f8471417f6); ?>
<?php endif; ?>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal785c8021fd1a6e19eb80cad4b837cda0)): ?>
<?php $attributes = $__attributesOriginal785c8021fd1a6e19eb80cad4b837cda0; ?>
<?php unset($__attributesOriginal785c8021fd1a6e19eb80cad4b837cda0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal785c8021fd1a6e19eb80cad4b837cda0)): ?>
<?php $component = $__componentOriginal785c8021fd1a6e19eb80cad4b837cda0; ?>
<?php unset($__componentOriginal785c8021fd1a6e19eb80cad4b837cda0); ?>
<?php endif; ?>

    <?php $__env->startPush('scripts'); ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('volunteer-application-form');
            
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Show loading state
                const submitBtn = form.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = 'Submitting...';
                submitBtn.disabled = true;
                
                // Simulate form submission
                setTimeout(() => {
                    alert('Thank you for your application! We will review it and contact you within 3-5 business days.');
                    form.reset();
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 2000);
            });
        });
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4619374cef299e94fd7263111d0abc69)): ?>
<?php $attributes = $__attributesOriginal4619374cef299e94fd7263111d0abc69; ?>
<?php unset($__attributesOriginal4619374cef299e94fd7263111d0abc69); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4619374cef299e94fd7263111d0abc69)): ?>
<?php $component = $__componentOriginal4619374cef299e94fd7263111d0abc69; ?>
<?php unset($__componentOriginal4619374cef299e94fd7263111d0abc69); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\ngo-web\resources\views/volunteer/index.blade.php ENDPATH**/ ?>