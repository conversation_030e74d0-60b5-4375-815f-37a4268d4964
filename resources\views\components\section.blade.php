@props([
    'background' => 'default', // default, muted, primary, card
    'padding' => 'default', // none, sm, default, lg, xl
    'container' => true,
    'id' => null
])

@php
$backgroundClasses = match($background) {
    'muted' => 'bg-muted',
    'primary' => 'bg-primary text-primary-foreground',
    'card' => 'bg-card',
    default => 'bg-background'
};

$paddingClasses = match($padding) {
    'none' => '',
    'sm' => 'py-8',
    'lg' => 'py-20',
    'xl' => 'py-24',
    default => 'py-16'
};

$classes = trim("{$backgroundClasses} {$paddingClasses}");
@endphp

<section {{ $id ? "id={$id}" : '' }} {{ $attributes->merge(['class' => $classes]) }}>
    @if($container)
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {{ $slot }}
        </div>
    @else
        {{ $slot }}
    @endif
</section>
