<?php

use Illuminate\Support\Facades\Route;

// Home Page
Route::get('/', function () {
    return view('home');
})->name('home');

// Who We Are Routes
Route::prefix('about')->name('about.')->group(function () {
    Route::get('/', function () {
        return view('about.index');
    })->name('index');
});

Route::get('/members', function () {
    return view('members');
})->name('members');

// What We Do Routes
Route::prefix('works')->name('works.')->group(function () {
    Route::get('/', function () {
        return view('works.index');
    })->name('index');
});

Route::prefix('training')->name('training.')->group(function () {
    Route::get('/', function () {
        return view('training.index');
    })->name('index');
});

Route::prefix('education')->name('education.')->group(function () {
    Route::get('/', function () {
        return view('education.index');
    })->name('index');
});

// Media Routes
Route::prefix('media')->name('media.')->group(function () {
    Route::get('/coverage', function () {
        return view('media.coverage');
    })->name('coverage');
});

Route::prefix('gallery')->name('gallery.')->group(function () {
    Route::get('/', function () {
        return view('gallery.index');
    })->name('index');
});

// Blog Routes
Route::prefix('blog')->name('blog.')->group(function () {
    Route::get('/', function () {
        return view('blog.index');
    })->name('index');

    Route::get('/{slug}', function ($slug) {
        // In a real application, you would fetch the post from database
        return view('blog.show', compact('slug'));
    })->name('show');
});

// Get Involved Routes
Route::prefix('volunteer')->name('volunteer.')->group(function () {
    Route::get('/', function () {
        return view('volunteer.index');
    })->name('index');
});

Route::prefix('contact')->name('contact.')->group(function () {
    Route::get('/', function () {
        return view('contact.index');
    })->name('index');
});
