@props([
    'variant' => 'default', // default, elevated, bordered, ghost
    'padding' => 'default', // none, sm, default, lg, xl
    'hover' => false,
    'clickable' => false,
    'href' => null
])

@php
$baseClasses = 'bg-card text-card-foreground rounded-xl transition-all duration-300 ease-out';

$variantClasses = match($variant) {
    'elevated' => 'shadow-medium border border-border/50 hover:shadow-strong transform hover:-translate-y-2 hover:border-primary/20',
    'bordered' => 'border border-border hover:border-primary/30 shadow-soft hover:shadow-medium',
    'ghost' => 'bg-transparent hover:bg-card/50 hover:shadow-soft',
    default => 'shadow-soft border border-border/50 hover:shadow-medium transform hover:-translate-y-1 hover:border-primary/15'
};

$paddingClasses = match($padding) {
    'none' => '',
    'sm' => 'p-4',
    'lg' => 'p-8',
    'xl' => 'p-12',
    default => 'p-6'
};

$hoverClasses = $hover ? 'hover:shadow-medium hover:-translate-y-1 hover:border-primary/20' : '';
$clickableClasses = $clickable ? 'cursor-pointer hover:shadow-medium hover:-translate-y-1 hover:border-primary/20 hover:bg-primary/2' : '';

$classes = trim("{$baseClasses} {$variantClasses} {$paddingClasses} {$hoverClasses} {$clickableClasses}");
@endphp

@if($href)
    <a href="{{ $href }}" {{ $attributes->merge(['class' => $classes]) }}>
        {{ $slot }}
    </a>
@else
    <div {{ $attributes->merge(['class' => $classes]) }}>
        {{ $slot }}
    </div>
@endif
