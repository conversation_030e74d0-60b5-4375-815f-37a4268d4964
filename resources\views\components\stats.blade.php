@props([
    'stats' => [],
    'columns' => 'auto', // auto, 2, 3, 4
    'variant' => 'default' // default, cards, minimal
])

@php
$gridClasses = match($columns) {
    '2' => 'grid-cols-1 md:grid-cols-2',
    '3' => 'grid-cols-1 md:grid-cols-3',
    '4' => 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
    default => count($stats) <= 2 ? 'grid-cols-1 md:grid-cols-2' : (count($stats) === 3 ? 'grid-cols-1 md:grid-cols-3' : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4')
};
@endphp

<div class="grid {{ $gridClasses }} gap-6">
    @foreach($stats as $stat)
        @if($variant === 'cards')
            <x-card hover="true" class="text-center">
                @if(isset($stat['icon']))
                    <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                        <div class="w-6 h-6 text-primary">
                            {!! $stat['icon'] !!}
                        </div>
                    </div>
                @endif
                <div class="text-3xl md:text-4xl font-bold text-primary mb-2">
                    {{ $stat['value'] }}
                </div>
                <div class="text-sm font-medium text-muted-foreground uppercase tracking-wider">
                    {{ $stat['label'] }}
                </div>
                @if(isset($stat['description']))
                    <p class="text-sm text-muted-foreground mt-2">
                        {{ $stat['description'] }}
                    </p>
                @endif
            </x-card>
        @elseif($variant === 'minimal')
            <div class="text-center">
                <div class="text-2xl md:text-3xl font-bold text-foreground mb-1">
                    {{ $stat['value'] }}
                </div>
                <div class="text-sm text-muted-foreground">
                    {{ $stat['label'] }}
                </div>
            </div>
        @else
            <div class="text-center p-6 rounded-lg bg-card border border-border hover:shadow-md transition-smooth">
                @if(isset($stat['icon']))
                    <div class="w-16 h-16 bg-gradient-to-br from-primary to-primary-hover rounded-full flex items-center justify-center mx-auto mb-4">
                        <div class="w-8 h-8 text-primary-foreground">
                            {!! $stat['icon'] !!}
                        </div>
                    </div>
                @endif
                <div class="text-3xl md:text-4xl font-bold text-primary mb-2 counter" data-target="{{ preg_replace('/[^0-9]/', '', $stat['value']) }}">
                    {{ $stat['value'] }}
                </div>
                <div class="text-sm font-semibold text-foreground mb-1">
                    {{ $stat['label'] }}
                </div>
                @if(isset($stat['description']))
                    <p class="text-sm text-muted-foreground">
                        {{ $stat['description'] }}
                    </p>
                @endif
            </div>
        @endif
    @endforeach
</div>

@if($variant === 'default')
    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const counters = document.querySelectorAll('.counter');
            const options = {
                threshold: 0.5,
                rootMargin: '0px 0px -100px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const counter = entry.target;
                        const target = parseInt(counter.dataset.target);
                        const originalText = counter.textContent;
                        
                        if (target && !counter.classList.contains('animated')) {
                            counter.classList.add('animated');
                            animateCounter(counter, target, originalText);
                        }
                    }
                });
            }, options);

            counters.forEach(counter => {
                observer.observe(counter);
            });

            function animateCounter(element, target, originalText) {
                let current = 0;
                const increment = target / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        element.textContent = originalText;
                        clearInterval(timer);
                    } else {
                        const suffix = originalText.replace(/[0-9]/g, '').replace(/[,\.]/g, '');
                        element.textContent = Math.floor(current).toLocaleString() + suffix;
                    }
                }, 40);
            }
        });
    </script>
    @endpush
@endif
