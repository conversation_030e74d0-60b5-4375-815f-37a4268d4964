# Vibrant Road Safety Awareness Foundation Website

A fully dynamic, mobile-first frontend built with Laravel Blade templates and Tailwind CSS, featuring a modern, clean UI/UX with dynamic color theming support.

## 🚀 Features

### Design & UI/UX
- **Mobile-first responsive design** - Optimized for all device sizes
- **Modern, clean interface** - Professional layout with smooth animations
- **Dynamic color theming** - CSS custom properties for easy theme switching
- **Accessibility focused** - WCAG compliant with proper focus management
- **Smooth animations** - Tailwind CSS transitions with custom easing

### Pages & Functionality
- **Home Page** - Hero banner, mission highlight, key stats, events preview, blog/media preview
- **Who We Are** - About Us (mission, vision, history) and Our Members (team showcase)
- **What We Do** - Our Works, Capacity Building & Training, Road Safety Education
- **Media** - Media Coverage (news articles) and Gallery (photo/video grid with filtering)
- **Blog** - Article listing with categories and individual post pages
- **Get Involved** - Volunteer application form and Contact Us with embedded map

### Technical Features
- **Laravel 12** with Blade templating
- **Tailwind CSS v4** for styling
- **Vite** for asset compilation
- **Component-based architecture** - Reusable Blade components
- **Dynamic content support** - Ready for backend integration
- **SEO optimized** - Proper meta tags and semantic HTML
- **Performance optimized** - Lazy loading, optimized images, minimal CSS/JS

## 📋 Requirements

- PHP 8.2+
- Composer
- Node.js 18+
- npm or yarn

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ngo-web
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install Node.js dependencies**
   ```bash
   npm install
   ```

4. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Build assets**
   ```bash
   npm run build
   # or for development
   npm run dev
   ```

6. **Start the development server**
   ```bash
   php artisan serve
   ```

7. **Visit the website**
   Open your browser and go to `http://localhost:8000`

## 🎨 Theme Customization

The website uses CSS custom properties for dynamic theming. You can easily customize colors by modifying the CSS variables in `resources/css/app.css`:

```css
:root {
    --primary: 220 38% 57%;        /* Blue primary */
    --safety-red: 0 84% 60%;       /* Safety red */
    --safety-yellow: 45 93% 47%;   /* Safety yellow */
    --safety-green: 142 76% 36%;   /* Safety green */
    --safety-orange: 25 95% 53%;   /* Safety orange */
}
```

### Theme Switching
The website includes a built-in theme switcher component that allows users to toggle between light and dark modes. The preference is saved in localStorage.

## 🧩 Components

The website uses reusable Blade components for consistency:

- `<x-button>` - Customizable buttons with variants
- `<x-card>` - Content cards with different styles
- `<x-hero>` - Page hero sections
- `<x-section>` - Page sections with consistent spacing
- `<x-stats>` - Statistics display with animations
- `<x-navigation>` - Responsive navigation bar
- `<x-footer>` - Site footer
- `<x-theme-switcher>` - Theme toggle component

## 📱 Responsive Design

The website is built with a mobile-first approach:

- **Mobile (320px+)** - Single column layout, hamburger menu
- **Tablet (768px+)** - Two-column layouts, expanded navigation
- **Desktop (1024px+)** - Multi-column layouts, full navigation
- **Large screens (1280px+)** - Optimized spacing and typography

## 🔧 Development

### Asset Compilation
```bash
# Development (with hot reload)
npm run dev

# Production build
npm run build

# Watch for changes
npm run watch
```

### Testing Routes
Run the included route testing script:
```bash
php test-routes.php
```

## 🌐 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 📈 Performance

The website is optimized for performance:
- Minimal CSS/JS bundle sizes
- Optimized images with proper sizing
- Lazy loading for images
- Efficient CSS with Tailwind's purging
- Smooth animations with hardware acceleration

## 🎯 SEO Features

- Semantic HTML structure
- Proper heading hierarchy
- Meta tags for social sharing
- Structured data ready
- Fast loading times
- Mobile-friendly design

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support or questions about the website:
- Email: <EMAIL>
- Phone: (*************
