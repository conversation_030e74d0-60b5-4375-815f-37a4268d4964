<nav id="navbar" class="fixed top-0 z-50 w-full transition-all duration-300 ease-in-out">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-20">
            <!-- Logo -->
            <div class="flex-shrink-0 z-50">
                <a href="{{ route('home') }}" class="flex items-center space-x-3 group">
                    <div class="w-12 h-12 bg-gradient-to-br from-safety-yellow to-safety-orange rounded-xl flex items-center justify-center transition-all duration-300 group-hover:scale-110 group-hover:rotate-3 shadow-lg">
                        <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                    <div class="hidden sm:block">
                        <span class="text-xl font-bold bg-gradient-to-r from-primary to-safety-green bg-clip-text text-transparent">VRSAF</span>
                        <p class="text-xs text-muted-foreground -mt-1 font-medium">Road Safety Foundation</p>
                    </div>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden lg:flex items-center justify-center flex-1">
                <div class="flex items-center space-x-8">
                    <a href="{{ route('home') }}" class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}">
                        <span>Home</span>
                    </a>

                    <!-- Who We Are Dropdown -->
                    <div class="relative dropdown-container" data-dropdown="who-we-are">
                        <button class="nav-link dropdown-trigger flex items-center space-x-2 {{ request()->routeIs('about.*') || request()->routeIs('members') ? 'active' : '' }}"
                                aria-expanded="false"
                                aria-haspopup="true"
                                data-dropdown-trigger="who-we-are">
                            <span>Who We Are</span>
                            <svg class="w-4 h-4 transition-all duration-300 dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="dropdown-menu absolute left-0 top-full mt-3 w-72 bg-card/98 backdrop-blur-xl border border-border/50 rounded-2xl shadow-2xl z-50 ring-1 ring-black/5"
                             data-dropdown-menu="who-we-are"
                             role="menu">
                            <div class="py-3">
                                <a href="{{ route('about.index') }}" class="dropdown-link" role="menuitem">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl flex items-center justify-center transition-all duration-300">
                                            <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <div class="font-semibold text-foreground">About Us</div>
                                            <div class="text-xs text-muted-foreground">Our mission & vision</div>
                                        </div>
                                        <svg class="w-4 h-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                        </svg>
                                    </div>
                                </a>
                                <a href="{{ route('members') }}" class="dropdown-link" role="menuitem">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-safety-green/20 to-safety-green/10 rounded-xl flex items-center justify-center transition-all duration-300">
                                            <svg class="w-5 h-5 text-safety-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <div class="font-semibold text-foreground">Our Members</div>
                                            <div class="text-xs text-muted-foreground">Meet our team</div>
                                        </div>
                                        <svg class="w-4 h-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                        </svg>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- What We Do Dropdown -->
                    <div class="relative dropdown-container" data-dropdown="what-we-do">
                        <button class="nav-link dropdown-trigger flex items-center space-x-2 {{ request()->routeIs('works.*') || request()->routeIs('training.*') || request()->routeIs('education.*') ? 'active' : '' }}"
                                aria-expanded="false"
                                aria-haspopup="true"
                                data-dropdown-trigger="what-we-do">
                            <span>What We Do</span>
                            <svg class="w-4 h-4 transition-all duration-300 dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="dropdown-menu absolute left-0 top-full mt-3 w-80 bg-card/98 backdrop-blur-xl border border-border/50 rounded-2xl shadow-2xl z-50 ring-1 ring-black/5"
                             data-dropdown-menu="what-we-do"
                             role="menu">
                            <div class="py-3">
                                <a href="{{ route('works.index') }}" class="dropdown-link" role="menuitem">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-safety-orange/20 to-safety-orange/10 rounded-xl flex items-center justify-center transition-all duration-300">
                                            <svg class="w-5 h-5 text-safety-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <div class="font-semibold text-foreground">Our Works</div>
                                            <div class="text-xs text-muted-foreground">Projects & initiatives</div>
                                        </div>
                                        <svg class="w-4 h-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                        </svg>
                                    </div>
                                </a>
                                <a href="{{ route('training.index') }}" class="dropdown-link" role="menuitem">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl flex items-center justify-center transition-all duration-300">
                                            <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <div class="font-semibold text-foreground">Capacity Building</div>
                                            <div class="text-xs text-muted-foreground">Training & development</div>
                                        </div>
                                        <svg class="w-4 h-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                        </svg>
                                    </div>
                                </a>
                                <a href="{{ route('education.index') }}" class="dropdown-link" role="menuitem">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-safety-green/20 to-safety-green/10 rounded-xl flex items-center justify-center transition-all duration-300">
                                            <svg class="w-5 h-5 text-safety-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <div class="font-semibold text-foreground">Road Safety Education</div>
                                            <div class="text-xs text-muted-foreground">Awareness programs</div>
                                        </div>
                                        <svg class="w-4 h-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                        </svg>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Media Dropdown -->
                    <div class="relative dropdown-container" data-dropdown="media">
                        <button class="nav-link dropdown-trigger flex items-center space-x-2 {{ request()->routeIs('media.*') || request()->routeIs('gallery.*') ? 'active' : '' }}"
                                aria-expanded="false"
                                aria-haspopup="true"
                                data-dropdown-trigger="media">
                            <span>Media</span>
                            <svg class="w-4 h-4 transition-all duration-300 dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="dropdown-menu absolute left-0 top-full mt-3 w-64 bg-card/98 backdrop-blur-xl border border-border/50 rounded-2xl shadow-2xl z-50 ring-1 ring-black/5"
                             data-dropdown-menu="media"
                             role="menu">
                            <div class="py-3">
                                <a href="{{ route('media.coverage') }}" class="dropdown-link" role="menuitem">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-safety-yellow/20 to-safety-yellow/10 rounded-xl flex items-center justify-center transition-all duration-300">
                                            <svg class="w-5 h-5 text-safety-yellow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <div class="font-semibold text-foreground">Media Coverage</div>
                                            <div class="text-xs text-muted-foreground">News & press releases</div>
                                        </div>
                                        <svg class="w-4 h-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                        </svg>
                                    </div>
                                </a>
                                <a href="{{ route('gallery.index') }}" class="dropdown-link" role="menuitem">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-safety-orange/20 to-safety-orange/10 rounded-xl flex items-center justify-center transition-all duration-300">
                                            <svg class="w-5 h-5 text-safety-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <div class="font-semibold text-foreground">Gallery</div>
                                            <div class="text-xs text-muted-foreground">Photos & videos</div>
                                        </div>
                                        <svg class="w-4 h-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                        </svg>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>

                    <a href="{{ route('blog.index') }}" class="nav-link {{ request()->routeIs('blog.*') ? 'active' : '' }}">
                        Blog
                    </a>

                    <!-- Get Involved Dropdown -->
                    <div class="relative dropdown-container" data-dropdown="get-involved">
                        <button class="nav-link dropdown-trigger flex items-center space-x-2 {{ request()->routeIs('volunteer.*') || request()->routeIs('contact.*') ? 'active' : '' }}"
                                aria-expanded="false"
                                aria-haspopup="true"
                                data-dropdown-trigger="get-involved">
                            <span>Get Involved</span>
                            <svg class="w-4 h-4 transition-all duration-300 dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                        <div class="dropdown-menu absolute left-0 top-full mt-3 w-64 bg-card/98 backdrop-blur-xl border border-border/50 rounded-2xl shadow-2xl z-50 ring-1 ring-black/5"
                             data-dropdown-menu="get-involved"
                             role="menu">
                            <div class="py-3">
                                <a href="{{ route('volunteer.index') }}" class="dropdown-link" role="menuitem">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-safety-green/20 to-safety-green/10 rounded-xl flex items-center justify-center transition-all duration-300">
                                            <svg class="w-5 h-5 text-safety-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <div class="font-semibold text-foreground">Become a Volunteer</div>
                                            <div class="text-xs text-muted-foreground">Join our mission</div>
                                        </div>
                                        <svg class="w-4 h-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                        </svg>
                                    </div>
                                </a>
                                <a href="{{ route('contact.index') }}" class="dropdown-link" role="menuitem">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl flex items-center justify-center transition-all duration-300">
                                            <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <div class="font-semibold text-foreground">Contact Us</div>
                                            <div class="text-xs text-muted-foreground">Get in touch</div>
                                        </div>
                                        <svg class="w-4 h-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                        </svg>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile menu button -->
            <div class="lg:hidden z-50">
                <button id="mobile-menu-button" type="button" class="relative inline-flex items-center justify-center p-3 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 text-foreground hover:bg-white/20 transition-all duration-300 group">
                    <span class="sr-only">Open main menu</span>
                    <div class="w-6 h-6 flex flex-col justify-center items-center">
                        <span id="hamburger-line-1" class="block w-6 h-0.5 bg-current transition-all duration-300 transform origin-center"></span>
                        <span id="hamburger-line-2" class="block w-6 h-0.5 bg-current mt-1.5 transition-all duration-300 transform origin-center"></span>
                        <span id="hamburger-line-3" class="block w-6 h-0.5 bg-current mt-1.5 transition-all duration-300 transform origin-center"></span>
                    </div>
                </button>
            </div>
        </div>
    </div>

    <!-- Full-screen Mobile menu -->
    <div id="mobile-menu" class="mobile-menu-overlay fixed inset-0 z-50 lg:hidden hidden opacity-0 transform translate-x-full transition-all duration-500 ease-in-out">
        <!-- Background with blur effect -->
        <div class="mobile-menu-backdrop absolute inset-0 bg-gradient-to-br from-primary via-primary-hover to-safety-green backdrop-blur-sm">
            <!-- Animated background pattern -->
            <div class="absolute inset-0 opacity-10">
                <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <pattern id="mobile-grid" width="10" height="10" patternUnits="userSpaceOnUse">
                            <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5"/>
                        </pattern>
                    </defs>
                    <rect width="100" height="100" fill="url(#mobile-grid)" />
                </svg>
            </div>
            <!-- Floating elements for visual appeal -->
            <div class="absolute top-20 left-10 w-32 h-32 bg-white/5 rounded-full blur-xl animate-pulse"></div>
            <div class="absolute bottom-32 right-8 w-24 h-24 bg-safety-yellow/10 rounded-full blur-lg animate-pulse" style="animation-delay: 1s;"></div>
        </div>

        <!-- Menu content with safe area support -->
        <div class="mobile-menu-content relative h-full flex flex-col justify-center px-6 py-safe-area-inset-top pb-safe-area-inset-bottom transform translate-x-0 transition-transform duration-500 ease-in-out">
            <!-- Close button area for better UX -->
            <div class="absolute top-6 right-6 z-10">
                <button class="mobile-close-btn w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-300 hover:scale-110 active:scale-95">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <!-- Mobile Navigation Links -->
            <div class="mobile-nav-container space-y-4 max-h-screen overflow-y-auto scrollbar-hide">
                <a href="{{ route('home') }}" class="mobile-nav-link {{ request()->routeIs('home') ? 'active' : '' }}">
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                            </svg>
                        </div>
                        <span class="mobile-nav-text">Home</span>
                    </div>
                </a>

                <!-- Who We Are Mobile -->
                <div class="mobile-dropdown" data-mobile-dropdown="who-we-are">
                    <button class="mobile-nav-dropdown-trigger w-full {{ request()->routeIs('about.*') || request()->routeIs('members') ? 'active' : '' }}"
                            data-mobile-trigger="who-we-are">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                    </svg>
                                </div>
                                <span class="mobile-nav-text">Who We Are</span>
                            </div>
                            <svg class="w-5 h-5 transition-transform duration-300 mobile-dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </div>
                    </button>
                    <div class="mobile-dropdown-content" data-mobile-content="who-we-are">
                        <div class="pl-14 pt-4 space-y-3">
                            <a href="{{ route('about.index') }}" class="mobile-nav-sublink {{ request()->routeIs('about.*') ? 'active' : '' }}">
                                <div class="flex items-center space-x-3">
                                    <div class="w-2 h-2 bg-safety-yellow rounded-full"></div>
                                    <span>About Us</span>
                                </div>
                            </a>
                            <a href="{{ route('members') }}" class="mobile-nav-sublink {{ request()->routeIs('members') ? 'active' : '' }}">
                                <div class="flex items-center space-x-3">
                                    <div class="w-2 h-2 bg-safety-yellow rounded-full"></div>
                                    <span>Our Members</span>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- What We Do Mobile -->
                <div class="mobile-dropdown" data-mobile-dropdown="what-we-do">
                    <button class="mobile-nav-dropdown-trigger w-full {{ request()->routeIs('works.*') || request()->routeIs('training.*') || request()->routeIs('education.*') ? 'active' : '' }}"
                            data-mobile-trigger="what-we-do">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                    </svg>
                                </div>
                                <span class="mobile-nav-text">What We Do</span>
                            </div>
                            <svg class="w-5 h-5 transition-transform duration-300 mobile-dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </div>
                    </button>
                    <div class="mobile-dropdown-content" data-mobile-content="what-we-do">
                        <div class="pl-14 pt-4 space-y-3">
                            <a href="{{ route('works.index') }}" class="mobile-nav-sublink {{ request()->routeIs('works.*') ? 'active' : '' }}">
                                <div class="flex items-center space-x-3">
                                    <div class="w-2 h-2 bg-safety-yellow rounded-full"></div>
                                    <span>Our Works</span>
                                </div>
                            </a>
                            <a href="{{ route('training.index') }}" class="mobile-nav-sublink {{ request()->routeIs('training.*') ? 'active' : '' }}">
                                <div class="flex items-center space-x-3">
                                    <div class="w-2 h-2 bg-safety-yellow rounded-full"></div>
                                    <span>Training</span>
                                </div>
                            </a>
                            <a href="{{ route('education.index') }}" class="mobile-nav-sublink {{ request()->routeIs('education.*') ? 'active' : '' }}">
                                <div class="flex items-center space-x-3">
                                    <div class="w-2 h-2 bg-safety-yellow rounded-full"></div>
                                    <span>Education</span>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Media Mobile -->
                <div class="mobile-dropdown" data-mobile-dropdown="media">
                    <button class="mobile-nav-dropdown-trigger w-full {{ request()->routeIs('media.*') || request()->routeIs('gallery.*') ? 'active' : '' }}"
                            data-mobile-trigger="media">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                                    </svg>
                                </div>
                                <span class="mobile-nav-text">Media</span>
                            </div>
                            <svg class="w-5 h-5 transition-transform duration-300 mobile-dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </div>
                    </button>
                    <div class="mobile-dropdown-content" data-mobile-content="media">
                        <div class="pl-14 pt-4 space-y-3">
                            <a href="{{ route('media.coverage') }}" class="mobile-nav-sublink {{ request()->routeIs('media.*') ? 'active' : '' }}">
                                <div class="flex items-center space-x-3">
                                    <div class="w-2 h-2 bg-safety-yellow rounded-full"></div>
                                    <span>Media Coverage</span>
                                </div>
                            </a>
                            <a href="{{ route('gallery.index') }}" class="mobile-nav-sublink {{ request()->routeIs('gallery.*') ? 'active' : '' }}">
                                <div class="flex items-center space-x-3">
                                    <div class="w-2 h-2 bg-safety-yellow rounded-full"></div>
                                    <span>Gallery</span>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <a href="{{ route('blog.index') }}" class="mobile-nav-link {{ request()->routeIs('blog.*') ? 'active' : '' }}">
                    <div class="flex items-center space-x-4">
                        <div class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                        </div>
                        <span class="mobile-nav-text">Blog</span>
                    </div>
                </a>

                <!-- Get Involved Mobile -->
                <div class="mobile-dropdown" data-mobile-dropdown="get-involved">
                    <button class="mobile-nav-dropdown-trigger w-full {{ request()->routeIs('volunteer.*') || request()->routeIs('contact.*') ? 'active' : '' }}"
                            data-mobile-trigger="get-involved">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                    </svg>
                                </div>
                                <span class="mobile-nav-text">Get Involved</span>
                            </div>
                            <svg class="w-5 h-5 transition-transform duration-300 mobile-dropdown-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </div>
                    </button>
                    <div class="mobile-dropdown-content" data-mobile-content="get-involved">
                        <div class="pl-14 pt-4 space-y-3">
                            <a href="{{ route('volunteer.index') }}" class="mobile-nav-sublink {{ request()->routeIs('volunteer.*') ? 'active' : '' }}">
                                <div class="flex items-center space-x-3">
                                    <div class="w-2 h-2 bg-safety-yellow rounded-full"></div>
                                    <span>Volunteer</span>
                                </div>
                            </a>
                            <a href="{{ route('contact.index') }}" class="mobile-nav-sublink {{ request()->routeIs('contact.*') ? 'active' : '' }}">
                                <div class="flex items-center space-x-3">
                                    <div class="w-2 h-2 bg-safety-yellow rounded-full"></div>
                                    <span>Contact</span>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile Menu Footer -->
            <div class="mt-16 pt-8 border-t border-white/20">
                <div class="text-white/80 text-sm">
                    <p class="font-medium">Vibrant Road Safety Awareness Foundation</p>
                    <p class="text-white/60 mt-1">Making roads safer for everyone</p>
                </div>
            </div>
        </div>
    </div>
</nav>

<style>
/* Enhanced Navigation Styles */
.nav-link {
    @apply relative inline-block px-4 py-2.5 mx-1 text-sm font-semibold text-foreground/90 hover:text-primary transition-all duration-300 rounded-xl hover:bg-primary/8 backdrop-blur-sm border border-transparent hover:border-primary/15 whitespace-nowrap hover:shadow-soft;
}

/* Fix dropdown button spacing */
.nav-link.flex {
    @apply gap-2;
}

.nav-link.active {
    @apply text-primary bg-primary/12 backdrop-blur-sm border-primary/25 shadow-soft;
}

.nav-link.active::after {
    content: '';
    @apply absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-3 h-0.5 bg-primary rounded-full shadow-glow;
}

/* Modern Dropdown Styles */
.dropdown-menu {
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 9999;
    pointer-events: none;
}

.dropdown-container:hover .dropdown-menu,
.dropdown-container.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    pointer-events: auto;
}

.dropdown-container:hover .dropdown-arrow,
.dropdown-container.active .dropdown-arrow {
    transform: rotate(180deg);
}

.dropdown-link {
    @apply block px-4 py-3 text-sm text-foreground hover:text-primary hover:bg-primary/6 transition-all duration-300 rounded-xl mx-2 my-1 border border-transparent hover:border-primary/15 group hover:shadow-soft;
}

.dropdown-link:hover .w-10 {
    @apply scale-110 rotate-3 shadow-soft;
}

/* Enhanced Mobile Navigation Styles */
.mobile-nav-link {
    @apply block py-4 text-white hover:text-safety-yellow transition-all duration-300 transform hover:translate-x-2 rounded-xl hover:bg-white/10 px-4 -mx-4;
    min-height: 48px;
    display: flex;
    align-items: center;
    touch-action: manipulation;
}

.mobile-nav-link.active {
    @apply text-safety-yellow bg-white/10;
}

.mobile-nav-text {
    @apply text-lg font-medium tracking-wide;
}

/* Enhanced Mobile Dropdown Styles */
.mobile-nav-dropdown-trigger {
    @apply py-4 text-white hover:text-safety-yellow transition-all duration-300 text-left rounded-xl hover:bg-white/10 px-4 -mx-4;
    min-height: 48px;
    display: flex;
    align-items: center;
    touch-action: manipulation;
    width: calc(100% + 2rem);
}

.mobile-nav-dropdown-trigger.active {
    @apply text-safety-yellow bg-white/10;
}

/* Ensure mobile dropdowns start closed - force override any conflicting styles */
.mobile-dropdown-content {
    max-height: 0 !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
    height: 0 !important;
}

.mobile-dropdown-arrow {
    @apply transition-transform duration-300;
}

/* Only open dropdown when the container div has active class, not the button */
.mobile-dropdown.dropdown-open .mobile-dropdown-arrow {
    @apply rotate-180;
}

.mobile-dropdown.dropdown-open .mobile-dropdown-content {
    max-height: 500px !important;
    height: auto !important;
}

.mobile-nav-sublink {
    @apply block py-3 text-white/90 hover:text-safety-yellow transition-all duration-300 text-base rounded-lg hover:bg-white/5 px-3 -mx-3;
    min-height: 44px;
    display: flex;
    align-items: center;
    touch-action: manipulation;
}

.mobile-nav-sublink.active {
    @apply text-safety-yellow bg-white/10;
}

/* Navbar scroll effects */
.navbar-scrolled {
    @apply bg-white/95 backdrop-blur-md border-b border-white/20 shadow-lg;
}

.navbar-transparent {
    @apply bg-transparent;
}

/* Perfect Mobile Menu Animations */
.mobile-menu-overlay {
    opacity: 0;
    visibility: hidden;
    transform: translateX(100%);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(0px);
    will-change: transform, opacity;
}

.mobile-menu-overlay:not(.hidden) {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
    backdrop-filter: blur(8px);
}

.mobile-menu-overlay.mobile-menu-open {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateX(0) !important;
    backdrop-filter: blur(8px) !important;
}

.mobile-menu-overlay.mobile-menu-closing {
    opacity: 0 !important;
    visibility: visible !important;
    transform: translateX(100%) !important;
    backdrop-filter: blur(0px) !important;
}

/* Mobile menu backdrop animation */
.mobile-menu-backdrop {
    transform: scale(1.1);
    transition: transform 0.4s ease-out;
}

.mobile-menu-overlay:not(.hidden) .mobile-menu-backdrop,
.mobile-menu-overlay.mobile-menu-open .mobile-menu-backdrop {
    transform: scale(1);
}

/* Mobile menu content animation */
.mobile-nav-container {
    transform: translateY(30px);
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transition-delay: 0.1s;
}

.mobile-menu-overlay:not(.hidden) .mobile-nav-container,
.mobile-menu-overlay.mobile-menu-open .mobile-nav-container {
    transform: translateY(0);
    opacity: 1;
}

/* Enhanced mobile navigation links */
.mobile-nav-link {
    opacity: 0;
    transform: translateX(30px);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.mobile-menu-overlay:not(.hidden) .mobile-nav-link,
.mobile-menu-overlay.mobile-menu-open .mobile-nav-link {
    opacity: 1;
    transform: translateX(0);
}

/* Mobile dropdown content animations */
.mobile-dropdown-content {
    max-height: 0;
    opacity: 0;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(-10px);
}

.mobile-dropdown.dropdown-open .mobile-dropdown-content {
    opacity: 1;
    transform: translateY(0);
}

.mobile-dropdown-arrow {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-dropdown.dropdown-open .mobile-dropdown-arrow {
    transform: rotate(180deg);
}

/* Staggered animation delays for smooth entrance */
.mobile-nav-link:nth-child(1) { transition-delay: 0.1s; }
.mobile-nav-link:nth-child(2) { transition-delay: 0.15s; }
.mobile-nav-link:nth-child(3) { transition-delay: 0.2s; }
.mobile-nav-link:nth-child(4) { transition-delay: 0.25s; }
.mobile-nav-link:nth-child(5) { transition-delay: 0.3s; }
.mobile-nav-link:nth-child(6) { transition-delay: 0.35s; }
.mobile-nav-link:nth-child(7) { transition-delay: 0.4s; }

/* Mobile dropdown animations */
.mobile-dropdown {
    opacity: 0;
    transform: translateX(30px);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.mobile-menu-overlay:not(.hidden) .mobile-dropdown,
.mobile-menu-overlay.mobile-menu-open .mobile-dropdown {
    opacity: 1;
    transform: translateX(0);
}

/* Close button animation */
.mobile-close-btn {
    opacity: 0;
    transform: scale(0.8) rotate(90deg);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transition-delay: 0.2s;
}

.mobile-menu-overlay:not(.hidden) .mobile-close-btn,
.mobile-menu-overlay.mobile-menu-open .mobile-close-btn {
    opacity: 1;
    transform: scale(1) rotate(0deg);
}

/* Safe area support for modern devices */
.mobile-menu-content {
    padding-top: env(safe-area-inset-top, 1.5rem);
    padding-bottom: env(safe-area-inset-bottom, 1.5rem);
    padding-left: env(safe-area-inset-left, 1.5rem);
    padding-right: env(safe-area-inset-right, 1.5rem);
}

/* Hide scrollbar for mobile nav */
.scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
    display: none;
}

/* Mobile-first optimizations */
@media (max-width: 1023px) {
    /* Prevent zoom on input focus */
    .mobile-nav-link,
    .mobile-nav-dropdown-trigger,
    .mobile-nav-sublink {
        font-size: 16px;
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
    }

    /* Smooth scrolling for mobile */
    .mobile-nav-container {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }

    /* Better touch targets */
    .mobile-nav-link,
    .mobile-nav-dropdown-trigger {
        min-height: 48px;
        padding-top: 12px;
        padding-bottom: 12px;
    }

    .mobile-nav-sublink {
        min-height: 44px;
        padding-top: 10px;
        padding-bottom: 10px;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .mobile-menu-backdrop {
        background-attachment: fixed;
    }
}

/* Landscape mobile optimization */
@media (max-height: 500px) and (orientation: landscape) {
    .mobile-menu-content {
        padding-top: 1rem;
        padding-bottom: 1rem;
    }

    .mobile-nav-container {
        max-height: calc(100vh - 2rem);
        overflow-y: auto;
    }

    .mobile-nav-link,
    .mobile-nav-dropdown-trigger {
        min-height: 40px;
        padding-top: 8px;
        padding-bottom: 8px;
    }

    .mobile-nav-text {
        font-size: 1rem;
    }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    .mobile-menu-overlay,
    .mobile-menu-backdrop,
    .mobile-nav-container,
    .mobile-nav-link,
    .mobile-dropdown,
    .mobile-close-btn {
        transition: none !important;
        animation: none !important;
    }
}
</style>


