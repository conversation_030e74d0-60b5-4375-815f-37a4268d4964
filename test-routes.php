<?php
/**
 * Simple route testing script for VRSAF website
 * Run this with: php test-routes.php
 */

$routes = [
    '/' => 'Home Page',
    '/about' => 'About Us',
    '/members' => 'Our Members',
    '/works' => 'Our Works',
    '/training' => 'Training Programs',
    '/education' => 'Road Safety Education',
    '/media/coverage' => 'Media Coverage',
    '/gallery' => 'Gallery',
    '/blog' => 'Blog Index',
    '/blog/sample-post' => 'Blog Post',
    '/volunteer' => 'Volunteer Page',
    '/contact' => 'Contact Us'
];

$baseUrl = 'http://localhost:8000'; // Adjust this to your local server URL

echo "Testing VRSAF Website Routes\n";
echo "============================\n\n";

foreach ($routes as $route => $description) {
    $url = $baseUrl . $route;
    echo "Testing: {$description} ({$route})\n";
    
    // Simple check - in a real scenario you might use curl or Guzzle
    $headers = @get_headers($url);
    if ($headers && strpos($headers[0], '200') !== false) {
        echo "✅ PASS - Route accessible\n";
    } else {
        echo "❌ FAIL - Route not accessible or returns error\n";
    }
    echo "\n";
}

echo "Route testing completed!\n";
echo "\nTo run the development server:\n";
echo "php artisan serve\n";
echo "\nThen visit http://localhost:8000 to view the website.\n";
?>
