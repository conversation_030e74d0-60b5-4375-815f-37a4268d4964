@props([
    'slides' => [],
    'autoplay' => true,
    'interval' => 5000,
    'showDots' => true,
    'showArrows' => true,
    'height' => 'h-screen'
])

<div class="relative {{ $height }} overflow-hidden bg-gradient-to-br from-primary via-primary-hover to-safety-green">
    <!-- Slider Container -->
    <div id="hero-slider" class="relative w-full h-full">
        @foreach($slides as $index => $slide)
            <div class="hero-slide absolute inset-0 transition-all duration-1000 ease-in-out {{ $index === 0 ? 'opacity-100 scale-100' : 'opacity-0 scale-105' }}" 
                 data-slide="{{ $index }}">
                
                <!-- Background Image/Video -->
                @if(isset($slide['background_image']))
                    <div class="absolute inset-0 bg-cover bg-center bg-no-repeat" 
                         style="background-image: url('{{ $slide['background_image'] }}');">
                        <div class="absolute inset-0 bg-black/40"></div>
                    </div>
                @elseif(isset($slide['background_video']))
                    <video class="absolute inset-0 w-full h-full object-cover" autoplay muted loop>
                        <source src="{{ $slide['background_video'] }}" type="video/mp4">
                    </video>
                    <div class="absolute inset-0 bg-black/30"></div>
                @else
                    <!-- Gradient Background with Pattern -->
                    <div class="absolute inset-0 bg-gradient-to-br from-primary via-primary-hover to-safety-green">
                        <div class="absolute inset-0 opacity-10">
                            <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <pattern id="hero-pattern-{{ $index }}" width="20" height="20" patternUnits="userSpaceOnUse">
                                        <circle cx="10" cy="10" r="1" fill="white" opacity="0.3"/>
                                        <path d="M0 10h20M10 0v20" stroke="white" stroke-width="0.5" opacity="0.2"/>
                                    </pattern>
                                </defs>
                                <rect width="100" height="100" fill="url(#hero-pattern-{{ $index }})" />
                            </svg>
                        </div>
                    </div>
                @endif
                
                <!-- Content -->
                <div class="relative z-10 h-full flex items-center">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
                        <div class="max-w-4xl">
                            <!-- Slide Content -->
                            <div class="text-white">
                                @if(isset($slide['badge']))
                                    <div class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-white/10 backdrop-blur-sm border border-white/20 text-white mb-6 animate-fade-in-up" style="animation-delay: 0.2s;">
                                        {{ $slide['badge'] }}
                                    </div>
                                @endif
                                
                                <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold leading-tight mb-6 animate-fade-in-up" style="animation-delay: 0.4s;">
                                    {!! $slide['title'] ?? 'Welcome to VRSAF' !!}
                                </h1>
                                
                                @if(isset($slide['subtitle']))
                                    <p class="text-xl md:text-2xl text-white/90 mb-8 max-w-2xl animate-fade-in-up" style="animation-delay: 0.6s;">
                                        {{ $slide['subtitle'] }}
                                    </p>
                                @endif
                                
                                @if(isset($slide['description']))
                                    <p class="text-lg text-white/80 mb-10 max-w-3xl leading-relaxed animate-fade-in-up" style="animation-delay: 0.8s;">
                                        {{ $slide['description'] }}
                                    </p>
                                @endif
                                
                                @if(isset($slide['actions']))
                                    <div class="flex flex-col sm:flex-row gap-4 animate-fade-in-up" style="animation-delay: 1s;">
                                        @foreach($slide['actions'] as $action)
                                            <a href="{{ $action['url'] }}" 
                                               class="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl {{ $action['style'] ?? 'bg-safety-yellow text-black hover:bg-safety-yellow/90' }}">
                                                @if(isset($action['icon']))
                                                    <span class="mr-3">{!! $action['icon'] !!}</span>
                                                @endif
                                                {{ $action['text'] }}
                                            </a>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Floating Elements -->
                <div class="absolute top-20 right-20 w-32 h-32 bg-safety-yellow/20 rounded-full blur-xl animate-float"></div>
                <div class="absolute bottom-32 left-20 w-24 h-24 bg-safety-green/20 rounded-full blur-xl animate-float-delayed"></div>
            </div>
        @endforeach
    </div>
    
    @if($showArrows && count($slides) > 1)
        <!-- Navigation Arrows -->
        <button id="prev-slide" class="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 p-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-300 group">
            <svg class="w-6 h-6 transform group-hover:-translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
        </button>
        
        <button id="next-slide" class="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 p-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white hover:bg-white/20 transition-all duration-300 group">
            <svg class="w-6 h-6 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
        </button>
    @endif
    
    @if($showDots && count($slides) > 1)
        <!-- Dots Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
            <div class="flex space-x-3">
                @foreach($slides as $index => $slide)
                    <button class="slide-dot w-3 h-3 rounded-full transition-all duration-300 {{ $index === 0 ? 'bg-white scale-125' : 'bg-white/50 hover:bg-white/80' }}" 
                            data-slide="{{ $index }}"></button>
                @endforeach
            </div>
        </div>
    @endif
    
    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 right-8 z-20 animate-bounce">
        <div class="flex flex-col items-center text-white/80">
            <span class="text-sm font-medium mb-2">Scroll</span>
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
            </svg>
        </div>
    </div>
</div>

<style>
@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes float-delayed {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-15px);
    }
}

.animate-fade-in-up {
    animation: fade-in-up 0.8s ease-out forwards;
    opacity: 0;
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
    animation: float-delayed 8s ease-in-out infinite;
    animation-delay: 2s;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const slider = document.getElementById('hero-slider');
    const slides = slider.querySelectorAll('.hero-slide');
    const dots = document.querySelectorAll('.slide-dot');
    const prevBtn = document.getElementById('prev-slide');
    const nextBtn = document.getElementById('next-slide');
    
    let currentSlide = 0;
    let autoplayInterval;
    
    function showSlide(index) {
        slides.forEach((slide, i) => {
            if (i === index) {
                slide.classList.add('opacity-100', 'scale-100');
                slide.classList.remove('opacity-0', 'scale-105');
            } else {
                slide.classList.remove('opacity-100', 'scale-100');
                slide.classList.add('opacity-0', 'scale-105');
            }
        });
        
        dots.forEach((dot, i) => {
            if (i === index) {
                dot.classList.add('bg-white', 'scale-125');
                dot.classList.remove('bg-white/50');
            } else {
                dot.classList.remove('bg-white', 'scale-125');
                dot.classList.add('bg-white/50');
            }
        });
        
        currentSlide = index;
    }
    
    function nextSlide() {
        const next = (currentSlide + 1) % slides.length;
        showSlide(next);
    }
    
    function prevSlide() {
        const prev = (currentSlide - 1 + slides.length) % slides.length;
        showSlide(prev);
    }
    
    // Event listeners
    if (nextBtn) nextBtn.addEventListener('click', nextSlide);
    if (prevBtn) prevBtn.addEventListener('click', prevSlide);
    
    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => showSlide(index));
    });
    
    // Autoplay
    @if($autoplay && count($slides) > 1)
    function startAutoplay() {
        autoplayInterval = setInterval(nextSlide, {{ $interval }});
    }
    
    function stopAutoplay() {
        clearInterval(autoplayInterval);
    }
    
    startAutoplay();
    
    // Pause autoplay on hover
    slider.addEventListener('mouseenter', stopAutoplay);
    slider.addEventListener('mouseleave', startAutoplay);
    @endif
    
    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowLeft') prevSlide();
        if (e.key === 'ArrowRight') nextSlide();
    });
});
</script>
