@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

/* Root Theme Variables - Enhanced Light Mode Color System */
:root {
    /* Primary Brand Colors - Enhanced for better contrast and vibrancy */
    --primary: 217 91% 60%;          /* Vibrant blue primary */
    --primary-foreground: 0 0% 100%;
    --primary-hover: 217 91% 55%;
    --primary-light: 217 91% 95%;

    /* Secondary Colors - Softer, more elegant */
    --secondary: 210 20% 98%;
    --secondary-foreground: 222.2 84% 4.9%;
    --secondary-hover: 210 20% 94%;

    /* Accent Colors - Warmer and more inviting */
    --accent: 210 20% 98%;
    --accent-foreground: 222.2 84% 4.9%;
    --accent-hover: 210 20% 94%;

    /* Background Colors - Pure and clean */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Muted Colors - Better readability */
    --muted: 210 20% 97%;
    --muted-foreground: 215.4 16.3% 44%;

    /* Border and Input - Subtle but defined */
    --border: 214.3 31.8% 88%;
    --input: 214.3 31.8% 88%;
    --ring: 217 91% 60%;

    /* Destructive - More balanced red */
    --destructive: 0 72% 51%;
    --destructive-foreground: 0 0% 100%;

    /* Success - More vibrant green */
    --success: 142 69% 45%;
    --success-foreground: 0 0% 100%;

    /* Warning - Warmer orange */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;

    /* Road Safety Theme Colors - Enhanced vibrancy */
    --safety-red: 0 72% 51%;
    --safety-yellow: 45 96% 53%;
    --safety-green: 142 69% 45%;
    --safety-orange: 25 95% 53%;

    /* Additional UI Colors */
    --surface: 0 0% 99%;
    --surface-foreground: 222.2 84% 4.9%;
    --outline: 214.3 31.8% 88%;
    --outline-variant: 214.3 31.8% 92%;

    /* Radius - More modern */
    --radius: 0.75rem;
}

/* Dark theme support (optional for future) */
.dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 220 38% 57%;
    --primary-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 220 38% 57%;
}

@theme {
    --font-sans: 'Inter', 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';

    /* Enhanced color palette using CSS variables */
    --color-primary: hsl(var(--primary));
    --color-primary-foreground: hsl(var(--primary-foreground));
    --color-primary-hover: hsl(var(--primary-hover));
    --color-primary-light: hsl(var(--primary-light));

    --color-secondary: hsl(var(--secondary));
    --color-secondary-foreground: hsl(var(--secondary-foreground));
    --color-secondary-hover: hsl(var(--secondary-hover));

    --color-accent: hsl(var(--accent));
    --color-accent-foreground: hsl(var(--accent-foreground));
    --color-accent-hover: hsl(var(--accent-hover));

    --color-background: hsl(var(--background));
    --color-foreground: hsl(var(--foreground));
    --color-card: hsl(var(--card));
    --color-card-foreground: hsl(var(--card-foreground));

    --color-muted: hsl(var(--muted));
    --color-muted-foreground: hsl(var(--muted-foreground));

    --color-border: hsl(var(--border));
    --color-input: hsl(var(--input));
    --color-ring: hsl(var(--ring));

    --color-destructive: hsl(var(--destructive));
    --color-destructive-foreground: hsl(var(--destructive-foreground));

    --color-success: hsl(var(--success));
    --color-success-foreground: hsl(var(--success-foreground));

    --color-warning: hsl(var(--warning));
    --color-warning-foreground: hsl(var(--warning-foreground));

    /* Enhanced Road Safety Theme Colors */
    --color-safety-red: hsl(var(--safety-red));
    --color-safety-yellow: hsl(var(--safety-yellow));
    --color-safety-green: hsl(var(--safety-green));
    --color-safety-orange: hsl(var(--safety-orange));

    /* Additional UI Colors */
    --color-surface: hsl(var(--surface));
    --color-surface-foreground: hsl(var(--surface-foreground));
    --color-outline: hsl(var(--outline));
    --color-outline-variant: hsl(var(--outline-variant));
}

/* Enhanced utility classes for smooth animations */
.transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-bounce {
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.transition-spring {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Modern glassmorphism effects - Enhanced */
.glass {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-subtle {
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced shadows with better depth */
.shadow-glow {
    box-shadow: 0 0 20px rgba(var(--primary), 0.3);
}

.shadow-glow-yellow {
    box-shadow: 0 0 30px rgba(var(--safety-yellow), 0.4);
}

.shadow-soft {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.06);
}

.shadow-medium {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.08);
}

.shadow-strong {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.16), 0 4px 12px rgba(0, 0, 0, 0.12);
}

/* Modern button styles */
.btn-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-hover)) 100%);
    color: hsl(var(--primary-foreground));
    border: none;
    border-radius: var(--radius);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(var(--primary), 0.3);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(var(--primary), 0.4);
}

/* Enhanced card styles */
.card-modern {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: calc(var(--radius) * 1.5);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border-color: hsl(var(--primary) / 0.3);
}

/* Smooth scroll behavior */
html {
    scroll-behavior: smooth;
}

/* Body padding for fixed navbar */
body {
    padding-top: 0;
}

/* Mobile menu body lock */
body.mobile-menu-open {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
    height: 100% !important;
}

/* Ensure content doesn't hide behind fixed navbar */
.content-with-navbar {
    margin-top: 80px;
}

/* Smooth mobile menu utilities */
.mobile-menu-smooth {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, opacity;
}

.mobile-dropdown-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: max-height, opacity, transform;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
    background: hsl(var(--primary));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary-hover));
}

/* Performance optimizations */
* {
    box-sizing: border-box;
}

img {
    max-width: 100%;
    height: auto;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Focus styles for accessibility */
*:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
}

/* Enhanced theme switcher utility classes */
.theme-light {
    /* Enhanced light theme is default - already defined in :root */
}

.theme-dark {
    /* Enhanced dark theme with better contrast and readability */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217 91% 65%;
    --primary-foreground: 222.2 84% 4.9%;
    --primary-hover: 217 91% 70%;
    --primary-light: 217 91% 15%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 217 91% 65%;
    --surface: 217.2 32.6% 15%;
    --surface-foreground: 210 40% 98%;
    --outline: 217.2 32.6% 25%;
    --outline-variant: 217.2 32.6% 20%;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }

    a,
    a:visited {
        text-decoration: underline;
    }

    a[href]:after {
        content: " (" attr(href) ")";
    }

    abbr[title]:after {
        content: " (" attr(title) ")";
    }

    .page-break {
        page-break-before: always;
    }
}
