@props([
    'position' => 'fixed', // fixed, relative, absolute
    'size' => 'default' // sm, default, lg
])

@php
$positionClasses = match($position) {
    'relative' => 'relative',
    'absolute' => 'absolute',
    default => 'fixed bottom-6 right-6 z-50'
};

$sizeClasses = match($size) {
    'sm' => 'w-10 h-10',
    'lg' => 'w-14 h-14',
    default => 'w-12 h-12'
};

$iconSize = match($size) {
    'sm' => 'w-4 h-4',
    'lg' => 'w-7 h-7',
    default => 'w-5 h-5'
};
@endphp

<div {{ $attributes->merge(['class' => $positionClasses]) }}>
    <button 
        id="theme-switcher"
        class="{{ $sizeClasses }} bg-card hover:bg-muted border border-border rounded-full shadow-lg flex items-center justify-center transition-smooth group"
        title="Toggle theme"
        aria-label="Toggle between light and dark theme"
    >
        <!-- Sun icon (light theme) -->
        <svg id="sun-icon" class="{{ $iconSize }} text-foreground transition-smooth" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
        </svg>
        
        <!-- Moon icon (dark theme) -->
        <svg id="moon-icon" class="{{ $iconSize }} text-foreground transition-smooth hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
        </svg>
    </button>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const themeSwitcher = document.getElementById('theme-switcher');
    const sunIcon = document.getElementById('sun-icon');
    const moonIcon = document.getElementById('moon-icon');
    const html = document.documentElement;
    
    // Check for saved theme preference or default to light mode
    const currentTheme = localStorage.getItem('theme') || 'light';
    
    // Apply the current theme
    function applyTheme(theme) {
        if (theme === 'dark') {
            html.classList.add('theme-dark');
            html.classList.remove('theme-light');
            sunIcon.classList.add('hidden');
            moonIcon.classList.remove('hidden');
        } else {
            html.classList.add('theme-light');
            html.classList.remove('theme-dark');
            sunIcon.classList.remove('hidden');
            moonIcon.classList.add('hidden');
        }
        localStorage.setItem('theme', theme);
    }
    
    // Initialize theme
    applyTheme(currentTheme);
    
    // Theme switcher click handler
    themeSwitcher.addEventListener('click', function() {
        const currentTheme = localStorage.getItem('theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        // Add a smooth transition effect
        html.style.transition = 'background-color 0.3s ease, color 0.3s ease';
        
        applyTheme(newTheme);
        
        // Remove transition after animation completes
        setTimeout(() => {
            html.style.transition = '';
        }, 300);
        
        // Optional: Dispatch custom event for other components to listen to
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme: newTheme }
        }));
    });
    
    // Listen for system theme changes
    if (window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        
        mediaQuery.addEventListener('change', function(e) {
            // Only auto-switch if user hasn't manually set a preference
            if (!localStorage.getItem('theme')) {
                applyTheme(e.matches ? 'dark' : 'light');
            }
        });
    }
    
    // Keyboard accessibility
    themeSwitcher.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            this.click();
        }
    });
});
</script>
@endpush

<style>
/* Theme switcher specific styles */
#theme-switcher:hover {
    transform: scale(1.05);
}

#theme-switcher:active {
    transform: scale(0.95);
}

/* Smooth theme transition */
html {
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Ensure icons transition smoothly */
#sun-icon, #moon-icon {
    transition: opacity 0.2s ease, transform 0.2s ease;
}

#theme-switcher:hover #sun-icon,
#theme-switcher:hover #moon-icon {
    transform: rotate(15deg);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    #theme-switcher,
    #sun-icon,
    #moon-icon,
    html {
        transition: none !important;
    }
}

/* Focus styles */
#theme-switcher:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
}
</style>
